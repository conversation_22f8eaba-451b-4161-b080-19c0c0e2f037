import { config } from 'dotenv';
import jwt from 'jsonwebtoken';

// Load environment variables
config({ path: '.env.local' });

const BASE_URL = 'http://localhost:3002';
const JWT_SECRET = process.env.JWT_SECRET;

// Test user credentials from the seeded data
const testUser = {
  email: '<EMAIL>',
  password: 'employee123'
};

async function testAPI() {
  try {
    console.log('🧪 Testing API Endpoints...\n');

    // Step 1: Test login
    console.log('1️⃣ Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login/whistleblower`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
        remember: false
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', {
      status: loginResponse.status,
      success: loginData.success,
      hasToken: !!loginData.token,
      user: loginData.user ? {
        id: loginData.user.id,
        email: loginData.user.email,
        role: loginData.user.role,
        companyId: loginData.user.companyId
      } : null
    });

    if (!loginData.success || !loginData.token) {
      console.error('❌ Login failed:', loginData.error);
      return;
    }

    const token = loginData.token;
    console.log('✅ Login successful\n');

    // Step 2: Verify token
    console.log('2️⃣ Verifying JWT token...');
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      console.log('Token payload:', {
        userId: decoded.userId,
        id: decoded.id,
        role: decoded.role,
        companyId: decoded.companyId,
        email: decoded.email
      });
      console.log('✅ Token is valid\n');
    } catch (tokenError) {
      console.error('❌ Token verification failed:', tokenError.message);
      return;
    }

    // Step 3: Test reports API
    console.log('3️⃣ Testing reports API...');
    const reportsResponse = await fetch(`${BASE_URL}/api/reports?limit=5`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    const reportsData = await reportsResponse.json();
    console.log('Reports API response:', {
      status: reportsResponse.status,
      success: reportsData.success,
      reportCount: reportsData.data ? reportsData.data.length : 0,
      error: reportsData.error
    });

    if (reportsData.success && reportsData.data) {
      console.log('Reports found:');
      reportsData.data.forEach((report, index) => {
        console.log(`   ${index + 1}. ${report.reportId || report._id}: ${report.title} (${report.status || 'No status'})`);
      });
    }
    console.log('');

    // Step 4: Test dashboard stats API
    console.log('4️⃣ Testing dashboard stats API...');
    const statsResponse = await fetch(`${BASE_URL}/api/dashboard/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    const statsData = await statsResponse.json();
    console.log('Stats API response:', {
      status: statsResponse.status,
      success: statsData.success,
      hasStats: !!statsData.data,
      error: statsData.error
    });

    if (statsData.success && statsData.data) {
      console.log('Dashboard stats:', {
        totalReports: statsData.data.totalReports,
        newReports: statsData.data.newReports,
        underReviewReports: statsData.data.underReviewReports,
        resolvedReports: statsData.data.resolvedReports
      });
    }
    console.log('');

    // Step 5: Test user reports API
    console.log('5️⃣ Testing user reports API...');
    const userReportsResponse = await fetch(`${BASE_URL}/api/user/reports?limit=5`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    const userReportsData = await userReportsResponse.json();
    console.log('User Reports API response:', {
      status: userReportsResponse.status,
      success: userReportsData.success,
      reportCount: userReportsData.data ? userReportsData.data.length : 0,
      error: userReportsData.error
    });

    if (userReportsData.success && userReportsData.data) {
      console.log('User reports found:');
      userReportsData.data.forEach((report, index) => {
        console.log(`   ${index + 1}. ${report.reportId || report._id}: ${report.title} (${report.status || 'No status'})`);
      });
    }

    console.log('\n✅ API testing complete');

  } catch (error) {
    console.error('❌ Error during API testing:', error);
  }
}

testAPI();
