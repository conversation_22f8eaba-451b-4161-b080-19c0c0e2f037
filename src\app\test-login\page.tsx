"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function TestLoginPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('employee123');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const router = useRouter();

  const handleLogin = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      console.log('Attempting login with:', { email, password });
      
      const response = await fetch('/api/auth/login/whistleblower', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          remember: false
        })
      });

      const data = await response.json();
      console.log('Login response:', data);
      
      setResult({
        status: response.status,
        success: data.success,
        data: data
      });

      if (data.success && data.token) {
        // Store the token
        localStorage.setItem('auth_token', data.token);
        
        // Create session data
        const sessionData = {
          userId: data.user.id,
          email: data.user.email,
          role: data.user.role,
          name: `${data.user.firstName} ${data.user.lastName}`.trim(),
          companyId: data.user.companyId,
          firstName: data.user.firstName,
          lastName: data.user.lastName,
          loginTime: new Date(),
          lastActivity: new Date(),
          sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        
        localStorage.setItem('user_session', JSON.stringify(sessionData));
        
        console.log('Login successful, stored token and session');
        
        // Test API call immediately
        await testApiCall(data.token);
        
        // Redirect to dashboard after a delay
        setTimeout(() => {
          router.push('/dashboard/whistleblower');
        }, 2000);
      }
    } catch (error) {
      console.error('Login error:', error);
      setResult({
        status: 'error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testApiCall = async (token: string) => {
    try {
      console.log('Testing API call with token...');
      
      const response = await fetch('/api/reports?limit=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });
      
      const data = await response.json();
      console.log('API test result:', data);
      
      setResult(prev => ({
        ...prev,
        apiTest: {
          status: response.status,
          success: data.success,
          reportCount: data.data ? data.data.length : 0,
          data: data
        }
      }));
    } catch (error) {
      console.error('API test error:', error);
      setResult(prev => ({
        ...prev,
        apiTest: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    }
  };

  const clearStorage = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_session');
    localStorage.removeItem('login_history');
    setResult(null);
    console.log('Storage cleared');
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6 text-center">Test Login</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleLogin}
              disabled={isLoading}
              className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            
            <button
              onClick={clearStorage}
              className="bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600"
            >
              Clear Storage
            </button>
          </div>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h3 className="font-medium mb-2">Result:</h3>
            <pre className="text-xs overflow-auto max-h-96 bg-white p-2 rounded border">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
        
        <div className="mt-4 text-center">
          <button
            onClick={() => router.push('/dashboard/whistleblower')}
            className="text-blue-500 hover:text-blue-700 text-sm"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}
