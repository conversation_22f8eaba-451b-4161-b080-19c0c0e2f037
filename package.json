{"name": "whistleblower-new", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev -p 3002 --turbopack", "dev:ws-standalone": "node src/lib/websocket/devServer.js", "dev:socket-standalone": "node src/lib/websocket/socketServer.js", "build": "next build", "build:no-lint": "cross-env SKIP_LINT=true next build", "start": "NODE_ENV=production node server.js", "start:next": "next start -p 3002", "lint": "next lint", "db:seed": "node scripts/run.mjs seed", "db:verify": "node scripts/run.mjs verify", "db:clean": "node scripts/run.mjs clean", "verify:services": "node scripts/verify-services.js"}, "type": "module", "overrides": {"react-is": "^19.1.0"}, "dependencies": {"@auth/core": "^0.34.2", "@hookform/resolvers": "^5.2.1", "@lexical/html": "^0.33.1", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/mark": "^0.33.1", "@lexical/markdown": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "express-rate-limit": "^8.0.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lexical": "^0.33.1", "lucide-react": "^0.525.0", "mongodb": "^6.18.0", "mongoose": "^8.17.1", "next": "15.3.5", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "recharts": "2.15.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.11", "@types/nodemailer": "^6.4.18", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "cross-env": "^7.0.3", "dotenv": "^17.2.1", "eslint": "^9.33.0", "eslint-config-next": "15.3.5", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}}