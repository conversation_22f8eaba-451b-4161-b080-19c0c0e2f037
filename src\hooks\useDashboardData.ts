"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import { apiClient } from '@/lib/api/client';
import { 
  ReportData, 
  RecentMessage, 
  ActivityItem, 
  Notification 
} from '@/lib/types';
import { ReportDocument } from '@/lib/db/models/interfaces';
import { 
  transformReportData, 
  transformConversationToMessage, 
  transformActivityData 
} from '@/lib/utils/dataTransformers';

interface DashboardData {
  reports: ReportData[];
  recentMessages: RecentMessage[];
  activities: ActivityItem[];
  notifications: Notification[];
  stats: {
    totalReports: number;
    newReports: number;
    underReviewReports: number;
    awaitingResponseReports: number;
    resolvedReports: number;
    highPriorityReports: number;
    periodComparison: {
      totalReportsChange: number;
      newReportsChange: number;
      resolvedReportsChange: number;
      period: string;
    };
  } | null;
}

interface DashboardState extends DashboardData {
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface DashboardActions {
  refreshAll: () => Promise<void>;
  refreshReports: () => Promise<void>;
  refreshMessages: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  refreshStats: () => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  navigateToReport: (reportId: string) => void;
  navigateToMessage: (conversationId: string) => void;
  updateReportStatus: (reportId: string, status: string) => Promise<void>;
  createReport: (reportData: ReportData | Omit<ReportData, 'id' | 'statusColor' | 'progress' | 'progressPercentage' | 'dateSubmitted' | 'lastUpdated'>) => Promise<void>;
}

export function useDashboardData(): DashboardState & DashboardActions {
  const { user, isAuthenticated } = useAuth();
  const [data, setData] = useState<DashboardData>({
    reports: [],
    recentMessages: [],
    activities: [],
    notifications: [],
    stats: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);

  // Fetch reports data
  const fetchReports = useCallback(async (): Promise<ReportData[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchReports: Not authenticated or no user ID');
      return [];
    }

    try {
      console.log('fetchReports: Making API call to /api/reports');
      const response = await apiClient.get<{ success: boolean; data: ReportDocument[] }>('/api/reports');
      console.log('fetchReports: Raw API response:', response);

      // The API client returns the response directly, so response should have success and data properties
      if (response && typeof response === 'object' && 'success' in response && response.success) {
        const apiData = (response as { success: boolean; data: ReportDocument[] }).data;
        console.log('fetchReports: Extracted API data:', apiData);

        if (Array.isArray(apiData)) {
          const transformedData = apiData.map(transformReportData);
          console.log('fetchReports: Transformed data:', transformedData);
          return transformedData;
        } else {
          console.log('fetchReports: API data is not an array:', apiData);
          return [];
        }
      } else {
        console.log('fetchReports: API response not successful:', response);
        return [];
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        console.log('fetchReports: Authentication error, user may need to re-login');
      }
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch recent messages
  const fetchMessages = useCallback(async (): Promise<RecentMessage[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchMessages: Not authenticated or no user ID');
      return [];
    }

    try {
      console.log('fetchMessages: Making API call to /api/conversations');
      const response = await apiClient.get<{ success: boolean; data: unknown[] }>('/api/conversations?limit=5');
      console.log('fetchMessages: API response:', response);
      
      if (response.success && response.data) {
        // Handle double-nested response structure
        let messagesData = response.data;
        if (messagesData && typeof messagesData === 'object' && 'success' in messagesData && 'data' in messagesData) {
          console.log('fetchMessages: Double-nested response detected, extracting inner data');
          messagesData = (messagesData as { data: unknown[] }).data || [];
        }

        if (Array.isArray(messagesData)) {
          const transformedData = messagesData.map((conv, index) =>
            transformConversationToMessage(conv, index, user.id)
          );
          console.log('fetchMessages: Transformed data:', transformedData);
          return transformedData;
        } else {
          console.log('fetchMessages: Data is not an array:', messagesData);
          return [];
        }
      }
      console.log('fetchMessages: API response not successful or no data');
      return [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch activities
  const fetchActivities = useCallback(async (): Promise<ActivityItem[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchActivities: Not authenticated or no user ID');
      return [];
    }
    
    try {
      console.log('fetchActivities: Making API call to /api/dashboard/recent-activity');
      const response = await apiClient.get<{ success: boolean; data: unknown[] }>('/api/dashboard/recent-activity?limit=10');
      console.log('fetchActivities: API response:', response);
      
      if (response.success && response.data) {
        // Handle double-nested response structure
        let activitiesData = response.data;
        if (activitiesData && typeof activitiesData === 'object' && 'success' in activitiesData && 'data' in activitiesData) {
          console.log('fetchActivities: Double-nested response detected, extracting inner data');
          activitiesData = (activitiesData as { data: unknown[] }).data || [];
        }

        if (Array.isArray(activitiesData)) {
          const transformedData = activitiesData.map(transformActivityData);
          console.log('fetchActivities: Transformed data:', transformedData);
          return transformedData;
        } else {
          console.log('fetchActivities: Data is not an array:', activitiesData);
          return [];
        }
      }
      console.log('fetchActivities: API response not successful or no data');
      return [];
    } catch (error) {
      console.error('Error fetching activities:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch notifications
  const fetchNotifications = useCallback(async (): Promise<Notification[]> => {
    if (!isAuthenticated || !user?.id) return [];
    
    try {
      const response = await apiClient.get<{ success: boolean; data: Notification[] }>(`/api/notifications?userId=${user.id}&limit=10`);
      if (response.success && response.data) {
        // Handle double-nested response structure
        let notificationsData = response.data;
        if (notificationsData && typeof notificationsData === 'object' && 'success' in notificationsData && 'data' in notificationsData) {
          console.log('fetchNotifications: Double-nested response detected, extracting inner data');
          notificationsData = (notificationsData as { data: Notification[] }).data || [];
        }

        if (Array.isArray(notificationsData)) {
          return notificationsData;
        } else {
          console.log('fetchNotifications: Data is not an array:', notificationsData);
          return [];
        }
      }
      return [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch dashboard stats
  const fetchStats = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchStats: Not authenticated or no user ID');
      return null;
    }
    
    try {
      console.log('fetchStats: Making API call to /api/dashboard/stats');
      const response = await apiClient.get<{ success: boolean; data: unknown }>("/api/dashboard/stats");
      console.log('fetchStats: Raw API response:', response);

      // The API client returns the response directly, so response should have success and data properties
      if (response && typeof response === 'object' && 'success' in response && response.success) {
        const statsData = (response as { success: boolean; data: unknown }).data;
        console.log('fetchStats: Extracted stats data:', statsData);
        return statsData;
      } else {
        console.log('fetchStats: API response not successful:', response);
        return null;
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        console.log('fetchStats: Authentication error, user may need to re-login');
      }
      return null;
    }
  }, [isAuthenticated, user?.id]);

  // Refresh all data
  const refreshAll = useCallback(async () => {
    if (!isMountedRef.current) return;

    console.log('refreshAll: Starting data refresh');
    console.log('refreshAll: Auth status', { isAuthenticated, userId: user?.id });
    console.log('refreshAll: Token exists', !!localStorage.getItem('auth_token'));
    setIsLoading(true);
    setError(null);

    try {
      console.log('refreshAll: Fetching all data...');
      const [reports, messages, activities, notifications, stats] = await Promise.all([
        fetchReports(),
        fetchMessages(),
        fetchActivities(),
        fetchNotifications(),
        fetchStats()
      ]) as [ReportData[], RecentMessage[], ActivityItem[], Notification[], DashboardData['stats']];

      console.log('refreshAll: All data fetched:', {
        reports: reports.length,
        messages: messages.length,
        activities: activities.length,
        notifications: notifications.length,
        stats: !!stats
      });

      if (isMountedRef.current) {
        const newData = {
          reports,
          recentMessages: messages,
          activities,
          notifications,
          stats
        };
        console.log('refreshAll: Setting new data:', newData);
        console.log('refreshAll: Reports count in new data:', newData.reports?.length);
        setData(newData);
        setLastUpdated(new Date());
        console.log('refreshAll: Data updated successfully');

        // Additional debugging for reports
        if (newData.reports && newData.reports.length > 0) {
          console.log('refreshAll: First report sample:', newData.reports[0]);
        } else {
          console.log('refreshAll: No reports in new data');
        }
      }
    } catch (error) {
      console.error('refreshAll: Error occurred:', error);
      if (isMountedRef.current) {
        setError(error instanceof Error ? error.message : 'Failed to fetch dashboard data');
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
        console.log('refreshAll: Loading state set to false');
      }
    }
  }, [fetchReports, fetchMessages, fetchActivities, fetchNotifications, fetchStats, isAuthenticated, user?.id]);

  // Individual refresh functions
  const refreshReports = useCallback(async () => {
    const reports = await fetchReports();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, reports }));
      setLastUpdated(new Date());
    }
  }, [fetchReports]);

  const refreshMessages = useCallback(async () => {
    const messages = await fetchMessages();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, recentMessages: messages }));
      setLastUpdated(new Date());
    }
  }, [fetchMessages]);

  const refreshNotifications = useCallback(async () => {
    const notifications = await fetchNotifications();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, notifications }));
      setLastUpdated(new Date());
    }
  }, [fetchNotifications]);

  const refreshStats = useCallback(async () => {
    const stats = await fetchStats() as DashboardData['stats'];
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, stats }));
      setLastUpdated(new Date());
    }
  }, [fetchStats]);

  // Mark notification as read
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      await apiClient.put(`/api/notifications/${notificationId}/read`, {});
      // Update local state
      setData(prev => ({
        ...prev,
        notifications: prev.notifications.map(notification =>
          notification._id === notificationId
            ? { ...notification, status: 'read' as const }
            : notification
        )
      }));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      await apiClient.put('/api/notifications/read-all', {});
      // Update local state
      setData(prev => ({
        ...prev,
        notifications: prev.notifications.map(notification => ({
          ...notification,
          status: 'read' as const
        }))
      }));
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, []);

  // Navigation actions
  const navigateToReport = useCallback((reportId: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = `/dashboard/whistleblower/my-reports?reportId=${reportId}`;
    }
  }, []);

  const navigateToMessage = useCallback((conversationId: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = `/dashboard/whistleblower/secure-message?conversationId=${conversationId}`;
    }
  }, []);

  // Update report status and refresh related data
  const updateReportStatus = useCallback(async (reportId: string, status: string) => {
    try {
      await apiClient.put(`/api/reports/${reportId}`, { status });
      // Refresh reports and stats to reflect the change
      await Promise.all([refreshReports(), refreshStats()]);
    } catch (error) {
      console.error('Error updating report status:', error);
    }
  }, [refreshReports, refreshStats]);

  // Create new report and refresh data
  const createReport = useCallback(async (reportData: ReportData | Omit<ReportData, 'id' | 'statusColor' | 'progress' | 'progressPercentage' | 'dateSubmitted' | 'lastUpdated'>) => {
    try {
      await apiClient.post('/api/reports', reportData);
      // Refresh all data to reflect the new report
      await refreshAll();
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }, [refreshAll]);

  // Initial data load
  useEffect(() => {
    console.log('useDashboardData: Initial data load effect triggered', {
      isAuthenticated,
      userId: user?.id,
      userRole: user?.role
    });
    
    if (isAuthenticated && user?.id) {
      console.log('useDashboardData: Conditions met, calling refreshAll');
      refreshAll();
    } else {
      console.log('useDashboardData: Conditions not met for data loading');
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id, user?.role, refreshAll]);

  // Real-time data synchronization
  useEffect(() => {
    if (!isAuthenticated || !user?.id) return;

    // Set up event listeners for real-time updates
    const handleReportUpdate = (event: CustomEvent) => {
      console.log('Report updated:', event.detail);
      refreshReports();
      refreshStats();
    };

    const handleNewMessage = (event: CustomEvent) => {
      console.log('New message received:', event.detail);
      refreshMessages();
    };

    const handleNewNotification = (event: CustomEvent) => {
      console.log('New notification:', event.detail);
      refreshNotifications();
    };

    const handleStatusChange = (event: CustomEvent) => {
      console.log('Status changed:', event.detail);
      refreshStats();
    };

    // Add event listeners
    window.addEventListener('report-updated', handleReportUpdate as EventListener);
    window.addEventListener('message-received', handleNewMessage as EventListener);
    window.addEventListener('notification-received', handleNewNotification as EventListener);
    window.addEventListener('status-changed', handleStatusChange as EventListener);

    // Auto-refresh data every 5 minutes
    const autoRefreshInterval = setInterval(() => {
      if (isMountedRef.current) {
        refreshAll();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      window.removeEventListener('report-updated', handleReportUpdate as EventListener);
      window.removeEventListener('message-received', handleNewMessage as EventListener);
      window.removeEventListener('notification-received', handleNewNotification as EventListener);
      window.removeEventListener('status-changed', handleStatusChange as EventListener);
      clearInterval(autoRefreshInterval);
    };
  }, [isAuthenticated, user?.id, refreshReports, refreshMessages, refreshNotifications, refreshStats, refreshAll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const returnValue = {
    ...data,
    isLoading,
    error,
    lastUpdated,
    refreshAll,
    refreshReports,
    refreshMessages,
    refreshNotifications,
    refreshStats,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    navigateToReport,
    navigateToMessage,
    updateReportStatus,
    createReport
  };

  console.log('useDashboardData: Returning data:', {
    reports: returnValue.reports?.length,
    stats: !!returnValue.stats,
    isLoading: returnValue.isLoading,
    error: returnValue.error,
    reportsArray: returnValue.reports
  });

  return returnValue;
}
