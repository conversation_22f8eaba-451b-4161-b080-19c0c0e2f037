"use client";

import { useDashboardData } from '@/hooks/useDashboardData';
import { useAuth } from '@/hooks/useAuth';

export default function TestDashboardDataPage() {
  const { user, isAuthenticated } = useAuth();
  const dashboardData = useDashboardData();

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Dashboard Data</h1>
      
      <div className="space-y-6">
        {/* Auth Status */}
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Authentication</h2>
          <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          <p><strong>User ID:</strong> {user?.id || 'None'}</p>
          <p><strong>User Email:</strong> {user?.email || 'None'}</p>
          <p><strong>User Role:</strong> {user?.role || 'None'}</p>
        </div>

        {/* Dashboard Data Status */}
        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Dashboard Data</h2>
          <p><strong>Is Loading:</strong> {dashboardData.isLoading ? 'Yes' : 'No'}</p>
          <p><strong>Error:</strong> {dashboardData.error || 'None'}</p>
          <p><strong>Reports Count:</strong> {dashboardData.reports?.length || 0}</p>
          <p><strong>Has Stats:</strong> {dashboardData.stats ? 'Yes' : 'No'}</p>
          <p><strong>Last Updated:</strong> {dashboardData.lastUpdated?.toLocaleString() || 'Never'}</p>
        </div>

        {/* Reports Data */}
        {dashboardData.reports && dashboardData.reports.length > 0 && (
          <div className="bg-yellow-100 p-4 rounded">
            <h2 className="text-lg font-semibold mb-2">Reports Data</h2>
            <div className="space-y-2">
              {dashboardData.reports.map((report, index) => (
                <div key={report.id} className="bg-white p-2 rounded border">
                  <p><strong>#{index + 1}:</strong> {report.id} - {report.title}</p>
                  <p><strong>Status:</strong> {report.status}</p>
                  <p><strong>Date:</strong> {report.dateSubmitted}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Stats Data */}
        {dashboardData.stats && (
          <div className="bg-purple-100 p-4 rounded">
            <h2 className="text-lg font-semibold mb-2">Stats Data</h2>
            <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(dashboardData.stats, null, 2)}
            </pre>
          </div>
        )}

        {/* Raw Debug */}
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Raw Dashboard Data</h2>
          <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-60">
            {JSON.stringify({
              reports: dashboardData.reports,
              isLoading: dashboardData.isLoading,
              error: dashboardData.error,
              hasStats: !!dashboardData.stats
            }, null, 2)}
          </pre>
        </div>

        {/* Manual Refresh */}
        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Manual Actions</h2>
          <div className="space-x-2">
            <button
              onClick={() => dashboardData.refreshAll()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Refresh All Data
            </button>
            <button
              onClick={() => dashboardData.refreshReports()}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Refresh Reports Only
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
