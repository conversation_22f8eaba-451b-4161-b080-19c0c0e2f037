"use client";

import { useState, useEffect } from 'react';
import { ReportTable } from '@/components/dashboard-components/whistleblower/dashboard/ReportTable';
import { ReportData } from '@/lib/types';

export default function TestTablePage() {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Sample test data
  const sampleReports: ReportData[] = [
    {
      id: 'WB-2025-0001',
      title: 'Test Report 1',
      status: 'New',
      statusColor: 'bg-blue-100 text-blue-800',
      dateSubmitted: '2025-01-15',
      lastUpdated: '2025-01-16',
      priority: 'High',
      category: 'Financial',
      progress: 'New',
      progressPercentage: 10
    },
    {
      id: 'WB-2025-0002',
      title: 'Test Report 2',
      status: 'Under Review',
      statusColor: 'bg-yellow-100 text-yellow-800',
      dateSubmitted: '2025-01-14',
      lastUpdated: '2025-01-16',
      priority: 'Medium',
      category: 'Safety',
      progress: 'Under Review',
      progressPercentage: 50
    },
    {
      id: 'WB-2025-0003',
      title: 'Test Report 3',
      status: 'Resolved',
      statusColor: 'bg-green-100 text-green-800',
      dateSubmitted: '2025-01-13',
      lastUpdated: '2025-01-16',
      priority: 'Low',
      category: 'Other',
      progress: 'Resolved',
      progressPercentage: 100
    }
  ];

  const loadSampleData = () => {
    setIsLoading(true);
    setTimeout(() => {
      setReports(sampleReports);
      setIsLoading(false);
      console.log('Sample data loaded:', sampleReports);
    }, 1000);
  };

  const clearData = () => {
    setReports([]);
    console.log('Data cleared');
  };

  const fetchRealData = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        alert('No auth token found. Please login first.');
        return;
      }

      const response = await fetch('/api/reports?limit=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();
      console.log('Real API data:', data);

      if (data.success && data.data) {
        // Transform the data
        const { transformReportData } = await import('@/lib/utils/dataTransformers');
        const transformedReports = data.data.map(transformReportData);
        console.log('Transformed reports:', transformedReports);
        setReports(transformedReports);
      } else {
        console.error('API call failed:', data);
        alert(`API Error: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error fetching real data:', error);
      alert(`Fetch Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Report Table</h1>
      
      <div className="mb-6 space-x-4">
        <button
          onClick={loadSampleData}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Load Sample Data
        </button>
        <button
          onClick={fetchRealData}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Fetch Real Data
        </button>
        <button
          onClick={clearData}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Clear Data
        </button>
      </div>

      <div className="mb-4 p-4 bg-gray-100 rounded">
        <h3 className="font-medium mb-2">Debug Info:</h3>
        <p><strong>Reports Count:</strong> {reports.length}</p>
        <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        <p><strong>Has Auth Token:</strong> {localStorage.getItem('auth_token') ? 'Yes' : 'No'}</p>
      </div>

      {reports.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 rounded">
          <h3 className="font-medium mb-2">Sample Report Data:</h3>
          <pre className="text-xs overflow-auto max-h-40">
            {JSON.stringify(reports[0], null, 2)}
          </pre>
        </div>
      )}

      <div className="border rounded-lg">
        <ReportTable
          reports={reports}
          isLoading={isLoading}
          onReportClick={(reportId) => {
            console.log('Report clicked:', reportId);
            alert(`Report clicked: ${reportId}`);
          }}
        />
      </div>
    </div>
  );
}
