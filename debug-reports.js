import { config } from 'dotenv';
import mongoose from 'mongoose';

// Load environment variables
config({ path: '.env.local' });

// Import models
import { Report, User, Company } from './src/lib/db/models/index.js';

async function debugReports() {
  try {
    console.log('🔍 Debugging Reports Dashboard Issue...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database\n');

    // Check total counts
    const reportCount = await Report.countDocuments();
    const userCount = await User.countDocuments();
    const companyCount = await Company.countDocuments();
    
    console.log('📊 Database Counts:');
    console.log(`   Reports: ${reportCount}`);
    console.log(`   Users: ${userCount}`);
    console.log(`   Companies: ${companyCount}\n`);

    // Get all users with their roles and companies
    const users = await User.find({}).populate('companyId', 'name').lean();
    console.log('👥 Users in database:');
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) - Company: ${user.companyId?.name || 'None'} - ID: ${user._id}`);
    });
    console.log('');

    // Get all reports with user and company info
    const reports = await Report.find({})
      .populate('userId', 'email firstName lastName role companyId')
      .populate('companyId', 'name')
      .lean();
    
    console.log('📋 Reports in database:');
    if (reports.length === 0) {
      console.log('   ❌ No reports found in database!');
    } else {
      reports.forEach(report => {
        const user = report.userId;
        const company = report.companyId;
        console.log(`   - Report ID: ${report.reportId || report._id}`);
        console.log(`     Title: ${report.title}`);
        console.log(`     Status: ${report.status || 'No status'}`);
        console.log(`     User: ${user?.email || 'Unknown'} (${user?.role || 'Unknown role'})`);
        console.log(`     Company: ${company?.name || 'No company'}`);
        console.log(`     Created: ${report.createdAt}`);
        console.log(`     Is Draft: ${report.isDraft || false}`);
        console.log('');
      });
    }

    // Test the DataService.getReports method for a whistleblower user
    const whistleblowerUser = users.find(u => u.role === 'whistleblower');
    if (whistleblowerUser) {
      console.log(`🔍 Testing DataService.getReports for whistleblower: ${whistleblowerUser.email}`);
      
      // Import DataService
      const { DataService } = await import('./src/lib/db/dataService.js');
      
      const userReports = await DataService.getReports(
        whistleblowerUser._id.toString(), 
        { limit: 10 }, 
        whistleblowerUser.companyId?.toString()
      );
      
      console.log(`   Found ${userReports.length} reports for this user:`);
      userReports.forEach(report => {
        console.log(`   - ${report.reportId || report._id}: ${report.title} (${report.status || 'No status'})`);
      });
    } else {
      console.log('❌ No whistleblower user found in database');
    }

    console.log('\n✅ Debug complete');
    
  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

debugReports();
