'use client';

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Mail, Shield } from 'lucide-react';

export default function ServicesTestPage() {
  const [emailTest, setEmailTest] = useState({
    to: '',
    loading: false,
    result: null as { 
      success: boolean; 
      message?: string; 
      error?: string;
      config?: { host: string; port: string; user: string };
      details?: string;
    } | null
  });

  const [oauthTest, setOauthTest] = useState({
    loading: false,
    result: null as { 
      success: boolean; 
      message?: string; 
      error?: string; 
      config?: Record<string, unknown>;
      missingConfigs?: string[];
      urls?: { google: string; microsoft: string };
    } | null
  });

  const testEmailService = async () => {
    if (!emailTest.to) {
      alert('Please enter an email address');
      return;
    }

    setEmailTest(prev => ({ ...prev, loading: true, result: null }));

    try {
      const response = await fetch('/api/test/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: emailTest.to,
          subject: 'Email Service Test',
          message: 'This is a test email to verify the email service configuration.'
        })
      });

      const result = await response.json();
      setEmailTest(prev => ({ ...prev, result }));
    } catch (error) {
      setEmailTest(prev => ({ 
        ...prev, 
        result: { success: false, error: 'Network error', details: error instanceof Error ? error.message : 'Unknown error' }
      }));
    } finally {
      setEmailTest(prev => ({ ...prev, loading: false }));
    }
  };

  const testOAuthConfig = async () => {
    setOauthTest(prev => ({ ...prev, loading: true, result: null }));

    try {
      const response = await fetch('/api/test/oauth');
      const result = await response.json();
      setOauthTest(prev => ({ ...prev, result }));
    } catch (error) {
      setOauthTest(prev => ({ 
        ...prev, 
        result: { success: false, error: 'Network error', details: error instanceof Error ? error.message : 'Unknown error' }
      }));
    } finally {
      setOauthTest(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Services Test Dashboard</h1>
          <p className="text-gray-600">Test email services and OAuth configuration</p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Email Service Test */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Service Test
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="email">Test Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email to test"
                  value={emailTest.to}
                  onChange={(e) => setEmailTest(prev => ({ ...prev, to: e.target.value }))}
                />
              </div>
              
              <Button 
                onClick={testEmailService} 
                disabled={emailTest.loading}
                className="w-full"
              >
                {emailTest.loading ? 'Sending Test Email...' : 'Send Test Email'}
              </Button>

              {emailTest.result && (
                <Alert variant={emailTest.result.success ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {emailTest.result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <AlertDescription>
                      {emailTest.result.success ? 'Email sent successfully!' : `Error: ${emailTest.result.error}`}
                    </AlertDescription>
                  </div>
                  {emailTest.result.config && (
                    <div className="mt-2 text-sm">
                      <strong>Configuration:</strong>
                      <ul className="list-disc list-inside mt-1">
                        <li>Host: {emailTest.result.config.host}</li>
                        <li>Port: {emailTest.result.config.port}</li>
                        <li>User: {emailTest.result.config.user}</li>
                      </ul>
                    </div>
                  )}
                  {emailTest.result.details && (
                    <div className="mt-2 text-sm text-red-600">
                      <strong>Details:</strong> {emailTest.result.details}
                    </div>
                  )}
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* OAuth Configuration Test */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                OAuth Configuration Test
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                Check if OAuth providers are properly configured
              </p>
              
              <Button 
                onClick={testOAuthConfig} 
                disabled={oauthTest.loading}
                className="w-full"
              >
                {oauthTest.loading ? 'Checking Configuration...' : 'Check OAuth Config'}
              </Button>

              {oauthTest.result && (
                <Alert variant={oauthTest.result.success ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {oauthTest.result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <AlertDescription>
                      {oauthTest.result.message}
                    </AlertDescription>
                  </div>
                  
                  {oauthTest.result.missingConfigs && oauthTest.result.missingConfigs.length > 0 && (
                    <div className="mt-2 text-sm text-red-600">
                      <strong>Missing configurations:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {oauthTest.result.missingConfigs.map((config: string) => (
                          <li key={config}>{config}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {oauthTest.result.urls && (
                    <div className="mt-2 text-sm">
                      <strong>OAuth URLs:</strong>
                      <ul className="list-disc list-inside mt-1">
                        <li>Google: {oauthTest.result.urls.google}</li>
                        <li>Microsoft: {oauthTest.result.urls.microsoft}</li>
                      </ul>
                    </div>
                  )}
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Links */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Test Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Authentication Pages</h3>
                <div className="space-y-2">
                  <Button variant="outline" asChild className="w-full justify-start">
                    <Link href="/login/whistleblower">Whistleblower Login</Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start">
                    <Link href="/login/admin">Admin Login</Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start">
                    <Link href="/signup/whistleblower">Whistleblower Signup</Link>
                  </Button>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Contact Forms</h3>
                <div className="space-y-2">
                  <Button variant="outline" asChild className="w-full justify-start">
                    <a href="/contact">Contact Form</a>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start">
                    <a href="/schedule-demo">Schedule Demo</a>
                  </Button>
                  <Button variant="outline" asChild className="w-full justify-start">
                    <a href="/newsletter">Newsletter Signup</a>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
