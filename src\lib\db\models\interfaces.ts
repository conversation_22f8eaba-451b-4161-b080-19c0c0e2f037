import { Document, Types } from 'mongoose';

// Document type aliases for backward compatibility
export type UserDocument = IUser;
export type ReportDocument = IReport;
export type ConversationDocument = IConversation;
export type MessageDocument = IMessage;
export type CompanyDocument = ICompany;
export type BlogDocument = IBlog;
export type PricingPlanDocument = IPricingPlan;

// User Interface
export interface IUser extends Document {
  _id: Types.ObjectId;
  email: string;
  hashedPassword?: string;
  firstName?: string;
  lastName?: string;
  companyId?: Types.ObjectId;
  role: 'whistleblower' | 'investigator' | 'admin';
  phoneNumber?: string;
  department?: string;
  jobTitle?: string;
  employeeId?: string;
  profileImage?: string;
  adminLevel?: string;
  permissions?: string[];
  isActive: boolean;
  lastLogin?: Date;
  lastActive?: Date;
  preferences?: {
    language?: string;
    timezone?: string;
    notifications?: {
      email?: boolean;
      push?: boolean;
      sms?: boolean;
      reportUpdates?: boolean;
      systemAlerts?: boolean;
      urgentReports?: boolean;
      weeklyDigest?: boolean;
    };
    theme?: string;
    systemSettings?: {
      autoAssignReports?: boolean;
      enableRealTimeNotifications?: boolean;
      showAdvancedAnalytics?: boolean;
      allowBulkActions?: boolean;
    };
    privacySettings?: {
      profileVisibility?: string;
      allowDirectContact?: boolean;
      shareReportingHistory?: boolean;
    };
  };
  twoFactor?: {
    enabled: boolean;
    method?: string;
    secret?: string;
    backupCodes?: string[];
    verificationCode?: string;
    verificationCodeExpires?: Date;
    attempts?: number;
  };
  securitySettings?: {
    sessionTimeout?: number;
    ipRestriction?: boolean;
    allowedIPs?: string[];
    auditLogging?: boolean;
    lastPasswordChange?: Date;
    passwordHistory?: Array<{
      hash: string;
      changedAt: Date;
    }>;
  };
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  emailVerified: boolean;
  emailVerificationToken?: string;
  failedLoginAttempts: number;
  accountLocked: boolean;
  accountLockedUntil?: Date;
  unlockToken?: string;
  unlockTokenExpires?: Date;
  passwordNeedsMigration?: boolean;
  passwordHashAlgorithm?: string;
  oauthProvider?: string;
  oauthId?: string;
  oauthProfile?: {
    sub?: string;
    name?: string;
    given_name?: string;
    family_name?: string;
    picture?: string;
    email_verified?: boolean;
  };
  isNewsletterSubscriber?: boolean;
  marketingConsent?: boolean;
  createdAt: Date;
  updatedAt: Date;
  fullName: string;
  isAccountLocked(): boolean;
}

// Report Interface
export interface IReport extends Document {
  _id: Types.ObjectId;
  reportId: string;
  referenceNumber?: string;
  userId: Types.ObjectId;
  companyId?: Types.ObjectId;
  title: string;
  description: string;
  category: string;
  dateOfOccurrence?: Date;
  location?: string;
  isAnonymous: boolean;
  incidentDate?: Date;
  incidentTime?: string;
  specificLocation?: string;
  departmentInvolved?: string;
  peopleInvolved?: string;
  hasWitnesses: boolean;
  witnessDetails?: string;
  hasEvidence: boolean;
  evidenceDescription?: string;
  evidenceFiles?: Array<{
    fileName: string;
    originalName: string;
    fileSize: number;
    mimeType: string;
    fileUrl: string;
    uploadedAt: Date;
    uploadedBy: Types.ObjectId;
    description?: string;
    isEncrypted: boolean;
  }>;
  urgencyLevel: string;
  financialImpact?: string;
  operationalImpact?: string;
  reputationalImpact?: string;
  hasPreviousReports: boolean;
  previousReportDetails?: string;
  additionalComments?: string;
  emailUpdates: boolean;
  smsUpdates: boolean;
  priority: string;
  status: string;
  isDraft: boolean;
  dateSubmitted?: Date;
  submittedAt?: Date;
  lastUpdated?: Date;
  draftStep?: number;
  lastSavedAt?: Date;
  evidence?: Array<{
    fileName: string;
    originalName: string;
    fileSize: number;
    mimeType: string;
    fileUrl: string;
    uploadedAt: Date;
    uploadedBy: Types.ObjectId;
    description?: string;
    isEncrypted: boolean;
  }>;
  assignedInvestigator?: Types.ObjectId;
  progress: number;
  estimatedCompletion?: Date;
  tags?: string[];
  statusHistory?: Array<{
    status: string;
    changedAt: Date;
    changedBy: Types.ObjectId;
    comment?: string;
  }>;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    submissionMethod?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Conversation Interface
export interface IConversation extends Document {
  _id: Types.ObjectId;
  reportId: Types.ObjectId;
  participants: Types.ObjectId[];
  status: string;
  lastMessageAt?: Date;
  isEncrypted: boolean;
  readBy?: Array<{
    userId: Types.ObjectId;
    readAt: Date;
    lastMessageId?: Types.ObjectId;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// Message Interface
export interface IMessage extends Document {
  _id: Types.ObjectId;
  conversationId: Types.ObjectId;
  senderId: Types.ObjectId;
  content: string;
  htmlContent?: string;
  messageType: string;
  isEncrypted: boolean;
  encryptionData?: {
    contentIv?: string;
    contentTag?: string;
    htmlContentIv?: string;
    htmlContentTag?: string;
  };
  readBy?: Array<{
    userId: Types.ObjectId;
    readAt: Date;
  }>;
  attachments?: Array<{
    fileName?: string;
    fileUrl?: string;
    fileSize?: number;
    mimeType?: string;
  }>;
  editedAt?: Date;
  isDeleted: boolean;
  deletedAt?: Date;
  replyTo?: Types.ObjectId;
  reactions?: Array<{
    userId: Types.ObjectId;
    emoji: string;
    createdAt: Date;
  }>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Company Interface
export interface ICompany extends Document {
  _id: Types.ObjectId;
  name: string;
  industry?: string;
  size?: string;
  logo?: string;
  website?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
  subscriptionStatus: string;
  subscriptionPlan?: Types.ObjectId;
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Blog Interface
export interface IBlog extends Document {
  _id: Types.ObjectId;
  title: string;
  content: string;
  author: Types.ObjectId;
  slug: string;
  excerpt?: string;
  featuredImage?: string;
  tags?: string[];
  category?: string;
  status: 'draft' | 'published' | 'archived';
  publishedAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Pricing Plan Interface
export interface IPricingPlan extends Document {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  price: number;
  currency: string;
  interval: 'monthly' | 'yearly';
  features: string[];
  maxUsers?: number;
  maxReports?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}