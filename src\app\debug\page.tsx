"use client";

import { useAuth } from '@/hooks/useAuth';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useEffect, useState } from 'react';

export default function DebugPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { reports, stats, isLoading: dataLoading, error } = useDashboardData();
  const [token, setToken] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<any>(null);

  useEffect(() => {
    // Check localStorage for token
    const storedToken = localStorage.getItem('auth_token');
    setToken(storedToken);

    // Check session data
    const sessionStr = localStorage.getItem('user_session');
    if (sessionStr) {
      try {
        setSessionData(JSON.parse(sessionStr));
      } catch (e) {
        console.error('Error parsing session data:', e);
      }
    }
  }, []);

  const testApiCall = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      console.log('Making test API call with token:', token ? 'Present' : 'Missing');

      const response = await fetch('/api/reports?limit=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();
      console.log('Test API response:', data);
      alert(`API Response: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error('Test API error:', error);
      alert(`API Error: ${error}`);
    }
  };

  const testDataTransformation = () => {
    // Test the data transformation with sample data
    const sampleReport = {
      _id: '123',
      reportId: 'WB-2025-0001',
      title: 'Test Report',
      status: 'New',
      createdAt: new Date(),
      updatedAt: new Date(),
      priority: 'High',
      category: 'Financial'
    };

    const { transformReportData } = require('@/lib/utils/dataTransformers');
    const transformed = transformReportData(sampleReport);
    console.log('Transformation test:', { input: sampleReport, output: transformed });
    alert(`Transformation Result: ${JSON.stringify(transformed, null, 2)}`);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Dashboard</h1>
      
      <div className="space-y-6">
        {/* Authentication Status */}
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Auth Loading:</strong> {authLoading ? 'Yes' : 'No'}</p>
            <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Has Token:</strong> {token ? 'Yes' : 'No'}</p>
            <p><strong>Token Preview:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}</p>
          </div>
        </div>

        {/* User Data */}
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">User Data</h2>
          <pre className="text-xs bg-white p-2 rounded overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        {/* Session Data */}
        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Session Data</h2>
          <pre className="text-xs bg-white p-2 rounded overflow-auto">
            {JSON.stringify(sessionData, null, 2)}
          </pre>
        </div>

        {/* Dashboard Data */}
        <div className="bg-yellow-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Dashboard Data</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Data Loading:</strong> {dataLoading ? 'Yes' : 'No'}</p>
            <p><strong>Error:</strong> {error || 'None'}</p>
            <p><strong>Reports Count:</strong> {reports?.length || 0}</p>
            <p><strong>Has Stats:</strong> {stats ? 'Yes' : 'No'}</p>
          </div>
          
          {reports && reports.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">Reports:</h3>
              <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(reports, null, 2)}
              </pre>
            </div>
          )}
          
          {stats && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">Stats:</h3>
              <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(stats, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Test Buttons */}
        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Manual Tests</h2>
          <div className="space-x-2">
            <button
              onClick={testApiCall}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test API Call
            </button>
            <button
              onClick={testDataTransformation}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Data Transform
            </button>
          </div>
        </div>

        {/* Console Logs */}
        <div className="bg-purple-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Instructions</h2>
          <p className="text-sm">
            1. Open browser developer tools (F12)<br/>
            2. Go to Console tab<br/>
            3. Look for logs starting with "useAuth:", "useDashboardData:", "fetchReports:", etc.<br/>
            4. Check Network tab for API requests<br/>
            5. Click "Test API Call" button to manually test the API
          </p>
        </div>
      </div>
    </div>
  );
}
